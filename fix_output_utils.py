#!/usr/bin/env python3
"""
修复 output_utils 删除后的导入错误
"""

import os
import re
from pathlib import Path

def fix_file(file_path):
    """修复单个文件"""
    print(f"修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 删除 output_utils 导入
    content = re.sub(r'from \.\.?output_utils import [^\n]+\n', '', content)
    content = re.sub(r'import \.\.?output_utils[^\n]*\n', '', content)
    
    # 替换函数调用
    replacements = {
        # 基本函数
        r'success\([^)]+\)': 'logging.info("✅ 操作成功")',
        r'error\([^)]+\)': 'logging.error("❌ 操作失败")',
        r'warning\([^)]+\)': 'logging.warning("⚠️ 警告")',
        r'info\([^)]+\)': 'logging.info("ℹ️ 信息")',
        r'debug\([^)]+\)': 'logging.debug("🔧 调试")',
        r'fatal\([^)]+\)': 'logging.error("❌ 致命错误")',
        
        # 链路追踪函数
        r'chain_start\([^)]+\)': '0  # 简化的链路追踪',
        r'chain_end\([^)]+\)': 'pass  # 简化的链路追踪',
        
        # 日志函数
        r'request_log\([^)]+\)': 'logging.info("📤 请求日志")',
        r'response_log\([^)]+\)': 'logging.info("📥 响应日志")',
        r'error_log\([^)]+\)': 'logging.error("🚨 错误日志")',
        r'step_log\([^)]+\)': 'logging.info("📍 步骤日志")',
        r'llm_request\([^)]+\)': 'logging.info("🧠 LLM请求")',
        r'llm_response\([^)]+\)': 'logging.info("🧠 LLM响应")',
        r'mcp_tool_call\([^)]+\)': 'logging.info("🔧 MCP工具调用")',
        r'mcp_tool_response\([^)]+\)': 'logging.info("🔧 MCP工具响应")',
        r'agent_step\([^)]+\)': 'logging.info("🤖 Agent步骤")',
        r'data_flow\([^)]+\)': 'logging.info("📊 数据流转")',
        
        # 标准错误处理
        r'standard_fatal_error\([^)]+\)': 'logging.error("❌ 标准错误")',
    }
    
    for pattern, replacement in replacements.items():
        content = re.sub(pattern, replacement, content)
    
    # 添加 logging 导入（如果还没有）
    if 'import logging' not in content and any(r in content for r in ['logging.info', 'logging.error', 'logging.warning', 'logging.debug']):
        # 在第一个 import 之后添加 logging 导入
        import_match = re.search(r'^(import [^\n]+\n)', content, re.MULTILINE)
        if import_match:
            content = content.replace(import_match.group(1), import_match.group(1) + 'import logging\n')
        else:
            content = 'import logging\n' + content
    
    # 只有内容发生变化时才写入文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ 已修复")
    else:
        print(f"  ⏭️ 无需修复")

def main():
    """主函数"""
    src_dir = Path('src')
    
    # 查找所有 Python 文件
    python_files = list(src_dir.rglob('*.py'))
    
    print(f"找到 {len(python_files)} 个 Python 文件")
    
    for file_path in python_files:
        try:
            fix_file(file_path)
        except Exception as e:
            print(f"  ❌ 修复失败: {e}")
    
    print("\n✅ 修复完成!")

if __name__ == '__main__':
    main()
